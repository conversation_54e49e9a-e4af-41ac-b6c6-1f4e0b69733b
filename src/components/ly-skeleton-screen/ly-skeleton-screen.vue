<template>
  <div v-if="isLoading" class="space-y-4">
    <div v-for="i in count" :key="i" class="bg-white rounded-2xl p-4 shadow-sm animate-pulse">
      <div class="flex items-center space-x-3 mb-4">
        <div class="w-12 h-12 bg-gray-200 rounded-full"></div>
        <div class="flex-1">
          <div class="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div class="h-3 bg-gray-200 rounded w-1/4"></div>
        </div>
      </div>
      <div class="h-20 bg-gray-200 rounded-xl mb-4"></div>
      <div class="flex space-x-4">
        <div class="h-8 bg-gray-200 rounded w-16"></div>
        <div class="h-8 bg-gray-200 rounded w-16"></div>
        <div class="h-8 bg-gray-200 rounded w-16"></div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  const props = withDefaults(
    defineProps<{
      isLoading: boolean;
      count: number;
    }>(),
    {
      isLoading: false,
      count: 3,
    },
  );
</script>
<style scoped lang="scss">
  // 骨架屏动画
  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .animate-pulse {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }
</style>
