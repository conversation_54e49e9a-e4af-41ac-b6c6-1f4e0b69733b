<template>
  <view class="upload-example">
    <view class="section">
      <text class="section-title">自动上传示例</text>
      <ly-upload
        v-model="autoUploadFiles"
        :limit="3"
        title="选择图片（自动上传）"
        :auto-upload="true"
        @uploadSuccess="handleUploadSuccess"
        @uploadError="handleUploadError"
      >
        <view class="upload-slot">
          <text class="i-mdi-camera-plus text-2xl text-gray-400"></text>
          <text class="text-sm text-gray-400 mt-1">点击上传</text>
        </view>
      </ly-upload>
    </view>

    <view class="section">
      <text class="section-title">手动上传示例</text>
      <ly-upload
        ref="manualUploadRef"
        v-model="manualUploadFiles"
        :limit="5"
        title="选择图片（手动上传）"
        :auto-upload="false"
      >
        <view class="upload-slot">
          <text class="i-mdi-file-upload text-2xl text-gray-400"></text>
          <text class="text-sm text-gray-400 mt-1">选择文件</text>
        </view>
      </ly-upload>
      
      <view class="mt-4 flex gap-2">
        <button 
          class="btn btn-primary" 
          @click="handleManualUpload"
          :disabled="manualUploadFiles.length === 0"
        >
          开始上传
        </button>
        <button 
          class="btn btn-secondary" 
          @click="handleGetResults"
        >
          获取结果
        </button>
      </view>
    </view>

    <view class="section">
      <text class="section-title">上传结果</text>
      <view class="result-container">
        <text class="result-text">{{ uploadResultText }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
  import monkey from '@/monkey';

  // 自动上传文件列表
  const autoUploadFiles = ref<any[]>([]);
  
  // 手动上传文件列表
  const manualUploadFiles = ref<any[]>([]);
  
  // 手动上传组件引用
  const manualUploadRef = ref<any>(null);
  
  // 上传结果文本
  const uploadResultText = ref('暂无上传结果');

  /**
   * 处理上传成功
   */
  const handleUploadSuccess = (result: any, file: any) => {
    console.log('🚀 ~ handleUploadSuccess ~ result:', result);
    console.log('🚀 ~ handleUploadSuccess ~ file:', file);
    
    uploadResultText.value = `上传成功: ${file.name || '文件'}\n结果: ${JSON.stringify(result.data, null, 2)}`;
    monkey.$helper.toast.success(`${file.name || '文件'} 上传成功`);
  };

  /**
   * 处理上传失败
   */
  const handleUploadError = (error: any, file: any) => {
    console.log('🚀 ~ handleUploadError ~ error:', error);
    console.log('🚀 ~ handleUploadError ~ file:', file);
    
    uploadResultText.value = `上传失败: ${file.name || '文件'}\n错误: ${error.msg || error.error || '未知错误'}`;
    monkey.$helper.toast.error(`${file.name || '文件'} 上传失败`);
  };

  /**
   * 手动上传
   */
  const handleManualUpload = async () => {
    if (!manualUploadRef.value) {
      monkey.$helper.toast.error('上传组件未准备好');
      return;
    }
    
    try {
      await manualUploadRef.value.upload();
      monkey.$helper.toast.success('所有文件上传完成');
    } catch (error) {
      console.error('🚀 ~ handleManualUpload ~ error:', error);
      monkey.$helper.toast.error('上传过程中出现错误');
    }
  };

  /**
   * 获取上传结果
   */
  const handleGetResults = () => {
    if (!manualUploadRef.value) {
      monkey.$helper.toast.error('上传组件未准备好');
      return;
    }
    
    const results = manualUploadRef.value.getUploadResults();
    console.log('🚀 ~ handleGetResults ~ results:', results);
    
    if (results.length > 0) {
      uploadResultText.value = `获取到 ${results.length} 个上传结果:\n${JSON.stringify(results, null, 2)}`;
      monkey.$helper.toast.success(`获取到 ${results.length} 个上传结果`);
    } else {
      uploadResultText.value = '暂无成功上传的文件';
      monkey.$helper.toast.info('暂无成功上传的文件');
    }
  };

  // 监听文件变化
  watch(autoUploadFiles, (newFiles) => {
    console.log('🚀 ~ autoUploadFiles changed:', newFiles);
  }, { deep: true });

  watch(manualUploadFiles, (newFiles) => {
    console.log('🚀 ~ manualUploadFiles changed:', newFiles);
  }, { deep: true });
</script>

<style scoped lang="scss">
  .upload-example {
    padding: 20px;
  }

  .section {
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .section-title {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
  }

  .upload-slot {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    background: #fafafa;
  }

  .btn {
    padding: 10px 20px;
    border-radius: 6px;
    border: none;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .btn-primary {
    background: #007aff;
    color: white;
    
    &:not(:disabled):hover {
      background: #0056cc;
    }
  }

  .btn-secondary {
    background: #f0f0f0;
    color: #333;
    
    &:not(:disabled):hover {
      background: #e0e0e0;
    }
  }

  .result-container {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  .result-text {
    font-family: monospace;
    font-size: 12px;
    color: #495057;
    white-space: pre-wrap;
    word-break: break-all;
  }
</style>
