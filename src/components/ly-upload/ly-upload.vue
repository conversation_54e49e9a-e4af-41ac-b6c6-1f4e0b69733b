<template>
  <uni-file-picker :sizeType="sizeType" :sourceType="sourceType" :auto-upload="false" :image-styles="listStyles" v-model="fileList" :file-mediatype="fileMediatype" :title="title" :limit="limit" @select="handleSelect" @delete="handleDelete">
    <slot />
  </uni-file-picker>
</template>
<script setup lang="ts">
  import monkey from '@/monkey';

  // 定义上传结果类型
  interface UploadResult {
    errcode: number;
    data?: any;
    msg?: string;
  }

  const props = withDefaults(
    defineProps<{
      modelValue: any[];
      limit: number;
      title: string;
      fileMediatype: string;
      imageStyles: Object;
      autoUpload?: boolean; // 是否自动上传
      sizeType: string[];
      sourceType: string[];
      returnUploadResults?: boolean; // 是否返回上传结果而不是文件列表
    }>(),
    {
      limit: 1,
      title: '',
      fileMediatype: 'image',
      autoUpload: false,
      returnUploadResults: false,
      imageStyles: {},
      sizeType: ['compressed'],
      sourceType: ['camera', 'album'],
    },
  );

  const emit = defineEmits<{
    (e: 'update:modelValue', value: any[]): void;
    (e: 'uploadSuccess', result: UploadResult, file: any): void;
    (e: 'uploadError', error: any, file: any): void;
    (e: 'uploadProgress', progress: number, file: any): void;
    (e: 'fileDeleted', deletedFile: any): void;
  }>();

  // 文件列表
  const fileList = ref<any[]>(props.modelValue || []);

  // 上传结果列表（当 returnUploadResults 为 true 时使用）
  const uploadResults = ref<any[]>([]);

  // 文件路径和上传结果的映射关系
  const fileUploadMap = new Map<string, any>();

  // 计算样式
  const listStyles = computed(() => props.imageStyles);

  // 监听 modelValue 变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (props.returnUploadResults) {
        // 如果返回上传结果，需要从上传结果重建文件列表
        uploadResults.value = newValue || [];
        // 这里可能需要根据实际情况调整，因为 fileList 和 uploadResults 的结构不同
      } else {
        fileList.value = newValue || [];
      }
    },
    { deep: true },
  );

  // 监听 fileList 变化，同步到 modelValue
  watch(
    fileList,
    (newValue) => {
      if (!props.returnUploadResults) {
        // 如果不返回上传结果，则返回文件列表
        emit('update:modelValue', newValue);
      }
      // 如果返回上传结果，则由 uploadResults 的 watch 来处理
    },
    { deep: true },
  );

  // 监听上传结果变化
  watch(
    uploadResults,
    (newValue) => {
      if (props.returnUploadResults) {
        emit('update:modelValue', newValue);
      }
    },
    { deep: true },
  );

  /**
   * 处理文件选择
   */
  const handleSelect = async (e: any) => {
    console.log('🚀 ~ handleSelect ~ e:', e);

    const { tempFiles } = e;

    // 遍历选择的文件进行上传
    for (const file of tempFiles) {
      await uploadFile(file);
    }
  };

  /**
   * 上传单个文件
   */
  const uploadFile = async (file: any) => {
    try {
      monkey.$helper.toast.loading('上传中...');

      // 调用上传 API
      const uploadResult = await monkey.$api.auth.uploadImage(file.path);

      console.log('🚀 ~ uploadFile ~ uploadResult:', uploadResult);

      if (uploadResult.errcode === 200) {
        // 上传成功，更新文件信息
        const updatedFile = {
          ...file,
          url: uploadResult.data?.url || file.path,
          uploadResult: uploadResult.data,
          status: 'success',
        };

        // 更新文件列表中对应的文件
        const index = fileList.value.findIndex((f) => f.path === file.path);
        if (index !== -1) {
          fileList.value[index] = updatedFile;
        }

        // 如果需要返回上传结果，则处理上传结果
        if (props.returnUploadResults && uploadResult.data) {
          const formattedResult = {
            id: uploadResult.data.id || '',
            name: file.name || '',
            fileSize: uploadResult.data.fileSize || 0,
            mimeType: uploadResult.data.mimeType || '',
            fileExtension: uploadResult.data.fileExtension || '',
            url: uploadResult.data.url || '',
            createdTime: uploadResult.data.createdTime || new Date().toISOString(),
            modifiedTime: uploadResult.data.modifiedTime || new Date().toISOString(),
            createdBy: uploadResult.data.createdBy || '',
            modifiedBy: uploadResult.data.modifiedBy || '',
            remarks: uploadResult.data.remarks || '',
            schemaCode: uploadResult.data.schemaCode || '',
            refId: uploadResult.data.refId || uploadResult.data.id || '',
            bizObjectId: uploadResult.data.bizObjectId || '',
            bizPropertyCode: uploadResult.data.bizPropertyCode || '',
            deleted: false,
          };

          // 建立文件路径和上传结果的映射关系
          const filePath = file.path || file.url;
          if (filePath) {
            fileUploadMap.set(filePath, formattedResult);
          }

          // 检查是否已存在相同的文件，避免重复添加
          const existingIndex = uploadResults.value.findIndex((item) => item.refId === formattedResult.refId);
          if (existingIndex === -1) {
            uploadResults.value.push(formattedResult);
          } else {
            uploadResults.value[existingIndex] = formattedResult;
          }
        }

        monkey.$helper.toast.success('上传成功');
        emit('uploadSuccess', uploadResult, updatedFile);
      } else if (uploadResult.errcode === 8000) {
        monkey.$helper.toast.error(uploadResult.msg);  
        // 弹出登录弹窗
        monkey.$stores.useAuthModalStore().open();
      } else {
        // 上传失败
        const errorFile = {
          ...file,
          status: 'error',
          error: uploadResult.msg || '上传失败',
        };

        // 更新文件列表中对应的文件
        const index = fileList.value.findIndex((f) => f.path === file.path);
        if (index !== -1) {
          fileList.value[index] = errorFile;
        }

        monkey.$helper.toast.error(uploadResult.msg || '上传失败');
        emit('uploadError', uploadResult, errorFile);
      }
    } catch (error) {
      console.error('🚀 ~ uploadFile ~ error:', error);

      const errorFile = {
        ...file,
        status: 'error',
        error: '上传失败，请重试',
      };

      // 更新文件列表中对应的文件
      const index = fileList.value.findIndex((f) => f.path === file.path);
      if (index !== -1) {
        fileList.value[index] = errorFile;
      }

      monkey.$helper.toast.hideLoading();
      monkey.$helper.toast.error('上传失败，请重试');
      emit('uploadError', error, errorFile);
    }
  };

  /**
   * 处理文件删除
   */
  const handleDelete = (e: any) => {
    console.log('🚀 ~ handleDelete ~ e:', e);

    // uni-file-picker 删除事件参数结构：{ index, tempFile, tempFilePath }
    const { tempFile, tempFilePath, index } = e;

    // 如果需要返回上传结果，则处理上传结果的删除
    if (props.returnUploadResults) {
      const filePath = tempFile?.path || tempFile?.url || tempFilePath;

      if (filePath) {
        // 首先尝试从映射中查找对应的上传结果
        const uploadResult = fileUploadMap.get(filePath);
        if (uploadResult) {
          // 从上传结果列表中移除对应的文件
          const resultIndex = uploadResults.value.findIndex((item) => item.refId === uploadResult.refId);
          if (resultIndex !== -1) {
            uploadResults.value.splice(resultIndex, 1);
            console.log('🚀 ~ uploadResults after deletion:', uploadResults.value);
          }
          // 从映射中移除
          fileUploadMap.delete(filePath);
          console.log('🚀 ~ fileUploadMap after deletion:', fileUploadMap);
        } else if (tempFile && tempFile.uploadResult && tempFile.uploadResult.refId) {
          // 如果映射中没有找到，尝试通过 tempFile 中的 uploadResult
          const resultIndex = uploadResults.value.findIndex((item) => item.refId === tempFile.uploadResult.refId);
          if (resultIndex !== -1) {
            uploadResults.value.splice(resultIndex, 1);
            console.log('🚀 ~ uploadResults after deletion by uploadResult:', uploadResults.value);
          }
        } else {
          // 最后尝试通过文件名匹配（不太可靠，但作为备选方案）
          const fileName = filePath.split('/').pop();
          if (fileName) {
            const matchingIndex = uploadResults.value.findIndex((item) => {
              return item.url && item.url.includes(fileName);
            });
            if (matchingIndex !== -1) {
              uploadResults.value.splice(matchingIndex, 1);
              console.log('🚀 ~ uploadResults after deletion by filename:', uploadResults.value);
            }
          }
        }
      }
    }

    // 发出删除事件，让父组件知道文件被删除了
    emit('fileDeleted', e);
  };

  /**
   * 手动上传文件（当 autoUpload 为 false 时使用）
   */
  const upload = async () => {
    const pendingFiles = fileList.value.filter((file) => !file.status || file.status === 'error');

    for (const file of pendingFiles) {
      await uploadFile(file);
    }
  };

  /**
   * 获取上传结果
   */
  const getUploadResults = () => {
    return fileList.value.filter((file) => file.status === 'success').map((file) => file.uploadResult);
  };

  // 暴露方法给父组件
  defineExpose({
    upload,
    getUploadResults,
    fileList: readonly(fileList),
  });
</script>
