# ly-upload 上传组件

基于 `uni-file-picker` 封装的图片上传组件，集成了项目的 `uploadImage` API，支持自动上传和手动上传两种模式。

## 功能特性

- ✅ 支持图片选择和上传
- ✅ 自动上传模式（选择后立即上传）
- ✅ 手动上传模式（选择后手动触发上传）
- ✅ 上传进度提示
- ✅ 上传成功/失败回调
- ✅ 获取上传结果
- ✅ 自定义样式支持
- ✅ 文件数量限制

## 基本用法

### 自动上传模式

```vue
<template>
  <ly-upload
    v-model="fileList"
    :limit="3"
    title="选择图片"
    :auto-upload="true"
    @uploadSuccess="handleUploadSuccess"
    @uploadError="handleUploadError"
  />
</template>

<script setup>
const fileList = ref([]);

const handleUploadSuccess = (result, file) => {
  console.log('上传成功:', result, file);
};

const handleUploadError = (error, file) => {
  console.log('上传失败:', error, file);
};
</script>
```

### 手动上传模式

```vue
<template>
  <ly-upload
    ref="uploadRef"
    v-model="fileList"
    :limit="5"
    title="选择图片"
    :auto-upload="false"
  />
  
  <button @click="handleUpload">开始上传</button>
  <button @click="getResults">获取结果</button>
</template>

<script setup>
const fileList = ref([]);
const uploadRef = ref();

const handleUpload = async () => {
  await uploadRef.value.upload();
};

const getResults = () => {
  const results = uploadRef.value.getUploadResults();
  console.log('上传结果:', results);
};
</script>
```

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | Array | [] | 文件列表，支持 v-model |
| limit | Number | 1 | 最大文件数量 |
| title | String | '' | 组件标题 |
| fileMediatype | String | 'image' | 文件类型 |
| imageStyles | Object | 见下方 | 图片样式配置 |
| autoUpload | Boolean | true | 是否自动上传 |

### imageStyles 默认值

```javascript
{
  width: 64,
  height: 64,
  border: {
    color: '#ff5a5f',
    width: 2,
    style: 'dashed',
    radius: '2px',
  },
}
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | fileList | 文件列表更新 |
| uploadSuccess | (result, file) | 上传成功回调 |
| uploadError | (error, file) | 上传失败回调 |
| uploadProgress | (progress, file) | 上传进度回调 |

## Methods

通过 ref 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| upload | - | Promise | 手动上传所有待上传文件 |
| getUploadResults | - | Array | 获取所有上传成功的结果 |

## 上传结果格式

上传成功后，`uploadSuccess` 事件会返回以下格式的结果：

```javascript
{
  errcode: 200,
  data: {
    url: "上传后的文件URL",
    refId: "文件引用ID",
    // 其他服务器返回的数据
  },
  msg: "上传成功"
}
```

## 自定义插槽

组件支持自定义上传按钮样式：

```vue
<ly-upload v-model="fileList">
  <view class="custom-upload-btn">
    <text class="icon">📷</text>
    <text>点击上传</text>
  </view>
</ly-upload>
```

## 注意事项

1. 组件依赖项目的 `monkey.$api.auth.uploadImage` API
2. 上传过程中会显示 loading 提示
3. 文件选择后会自动添加到文件列表中
4. 上传失败的文件会标记为 error 状态
5. 建议在使用前确保用户已登录（API 需要 token）

## 完整示例

参考 `example.vue` 文件查看完整的使用示例。
