<template>
  <ly-layout>
    <!-- 主内容区域 -->
    <view class="p-4">
      <!-- 预约信息卡片 -->
      <view class="bg-white rounded-lg shadow-md mb-4 overflow-hidden">
        <view class="px-4 py-4">
          <view class="flex items-center mb-4">
            <view class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
              <view class="i-mdi-calendar text-blue-600 text-base"></view>
            </view>
            <text class="text-sm font-semibold text-gray-900">预约信息</text>
          </view>

          <view class="grid grid-cols-1 gap-4 text-sm">
            <view class="flex items-center justify-between py-3 border-b border-gray-100">
              <view class="flex items-center">
                <text class="i-mdi-calendar text-theme-blue text-xl mr-2"></text>
                <text class="text-gray-600 font-medium text-sm">入库日期</text>
              </view>
              <text class="text-gray-800 font-mono text-sm">{{ monkey.$dayjs(record.yyrq).format('YYYY年MM月DD日') }}</text>
            </view>
            <view class="flex items-center justify-between py-3 border-b border-gray-100">
              <view class="flex items-center">
                <text class="i-mdi-clock text-theme-blue text-xl mr-2"></text>
                <text class="text-gray-600 font-medium text-sm">时间段</text>
              </view>
              <text class="text-gray-800 font-mono text-sm">{{ record.yysjd }}</text>
            </view>
            <view class="flex items-center justify-between py-3 border-b border-gray-100">
              <view class="flex items-center">
                <text class="i-mdi-account text-theme-blue text-xl mr-2"></text>
                <text class="text-gray-600 font-medium text-sm">预约人</text>
              </view>
              <text class="text-gray-800 font-mono text-sm">{{ record.yyr }}</text>
            </view>
            <view class="flex items-center justify-between py-3">
              <view class="flex items-center">
                <text class="i-mdi-phone text-theme-blue text-xl mr-2"></text>
                <text class="text-gray-600 font-medium text-sm">联系电话</text>
              </view>
              <text class="text-gray-800 font-mono text-sm">{{ monkey.$helper.utils.hidePhone(record.yyrdh) }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 产品信息卡片 -->
      <view class="bg-white rounded-lg shadow-md mb-4 overflow-hidden">
        <view class="px-4 py-4">
          <view class="flex items-center justify-between mb-4">
            <view class="flex items-center">
              <view class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                <view class="i-mdi-package-variant text-green-600 text-base"></view>
              </view>
              <text class="text-sm font-semibold text-gray-900">入库产品</text>
            </view>
            <text class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full"> 共 {{ record.yymx?.length || 0 }} 种 </text>
          </view>

          <view v-if="!record.yymx || record.yymx.length === 0">
            <view class="text-center py-8 text-gray-500">
              <view class="i-mdi-inbox-outline text-4xl mb-2 mx-auto w-12 h-12"></view>
              <text class="block">暂无产品信息</text>
            </view>
          </view>

          <view v-else>
            <!-- 产品列表 -->
            <view >
              <view v-for="(item, index) in record.yymx" :key="index" class="border-b border-gray-100 py-4 last:border-b-0">
                <view class="flex items-center justify-between px-4">
                  <!-- 产品信息 -->
                  <view class="flex-1 pr-4">
                    <view class="flex items-center mb-2">
                      <text class="text-sm font-semibold text-gray-900">{{ item.rkyc }}</text>
                    </view>
                    <!-- 产品规格 -->
                    <view v-if="item.cpgg" class="mb-2">
                      <text class="text-xs text-gray-500">规格：{{ item.cpgg }}</text>
                    </view>
                  </view>

                  <!-- 数量信息 -->
                  <view class="flex items-center space-x-8">
                    <!-- 数量 -->
                    <view class="text-right min-w-[60px]">
                      <text class="text-base font-bold text-gray-900 block">{{ item.rksl }}</text>
                      <text class="text-xs text-gray-500 font-medium">件</text>
                    </view>

                    <!-- 分隔线 -->
                    <view class="w-px h-8 bg-gray-200"></view>

                    <!-- 重量 -->
                    <view class="text-right min-w-[60px]">
                      <text class="text-base font-bold text-blue-600 block">{{ item.rkzl }}</text>
                      <text class="text-xs text-blue-500 font-medium">KG</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 详细信息卡片 -->
      <view class="bg-white rounded-lg shadow-md mb-4 overflow-hidden">
        <view class="px-4 py-4">
          <view class="flex items-center mb-4">
            <view class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center mr-3">
              <view class="i-mdi-clipboard-text text-gray-600 text-base"></view>
            </view>
            <text class="text-sm font-semibold text-gray-900">详细信息</text>
          </view>

          <view class="py-3">
            <text class="text-gray-600 mb-2 block">备注说明</text>
            <view class="bg-gray-50 rounded-lg p-3">
              <text class="text-gray-900 leading-relaxed">{{ record.yuany || '无备注说明' }}</text>
            </view>
          </view>
        </view>
      </view>
      <ly-line-bar></ly-line-bar>
    </view>
    <ly-fixed-btns :buttons="buttons"></ly-fixed-btns>
  </ly-layout>
</template>

<script setup lang="ts">
  import type { UserTypes } from '@/monkey/types';
  import monkey from '@/monkey';

  // 入库记录详情数据
  const record = ref<UserTypes.UserReservationInboundForm>({});

  // 按钮
  const buttons = ref<any[]>([]);

  // 状态相关方法
  const getStatusClass = (statusCode: string) => {
    const statusClasses = {
      shz: 'bg-blue-100 text-blue-700', // 审核中
      shtg: 'bg-green-100 text-green-700', // 已通过
      shwtg: 'bg-red-100 text-red-700', // 未通过
      wfk: 'bg-yellow-100 text-yellow-700', // 未付款
      yfk: 'bg-green-100 text-green-700', // 已付款
    };
    return statusClasses[statusCode as keyof typeof statusClasses] || 'bg-gray-100 text-gray-700';
  };

  const handleDelete = async () => {
    console.log('删除');
    const modal = await monkey.$helper.toast.modal({
      title: '提示',
      content: '确定删除该订单吗？',
      confirmText: '确定',
      cancelText: '取消',
    });
    if (!modal) return;
    const { errcode, data, errmsg } = await monkey.$api.authine.deleteAuthineData(
      monkey.$helper.param.getAuthineFormDeleteParams({
        ids: [record.value.id],
        schemaCode: 'yyrkd',
      }),
    );
    if (errcode === 0 && data) {
      monkey.$helper.toast.success(errmsg);
      uni.$emit('ORDER_DETAIL', { action: 'delete', id: record.value.id });
      monkey.$router.navigateBack(1, { delay: 1000 });
    }
  };

  const handlePay = () => {
    monkey.$helper.toast.success('暂未开放');
  };

  const getRecord = async (options: any) => {
    const params = monkey.$helper.param.getAuthineFormParams({
      objectId: options.id,
      schemaCode: 'yyrkd', // 入库预约单管理
    });
    const { errcode, data } = await monkey.$api.authine.getAuthineForm(params);
    if (errcode === 0 && data) {
      record.value = data.bizObject.data;
      if (record.value.sftg_key === 'shz') {
        buttons.value.push({ text: '删除', icon: 'i-mdi-delete', type: 'danger', click: () => handleDelete() });
      }
      if (record.value.sftg_key === 'wfk') {
        buttons.value.push({ text: '立即支付', icon: 'i-mdi-cash', type: 'primary', click: () => handlePay() });
      }
    }
  };

  onLoad((options) => {
    getRecord(options);
  });
</script>

<style scoped>
  /* 自定义样式 */
</style>
