/**
 * 判断是否支持安全区
 * @returns {boolean} 是否支持安全区
 */
export const hasSafeArea = (): boolean => {
  try {
    const systemInfo = uni.getSystemInfoSync();
    return systemInfo.safeAreaInsets?.bottom > 0;
  } catch (error) {
    console.warn('获取系统信息失败:', error);
    return false;
  }
};

// 导航栏渐变阈值常量
const NAV_THRESHOLD = 200;
const NAV_BASE_COLOR = '16, 185, 129';

/**
 * 获取导航栏背景颜色
 * @param scrollTop 滚动距离
 * @returns 导航栏背景颜色 rgba 格式
 */
export const getNavBarBgColor = (scrollTop: number): string => {
  const opacity = Math.min(scrollTop / NAV_THRESHOLD, 1);
  return `rgba(${NAV_BASE_COLOR}, ${opacity})`;
};

/**
 * 获取导航栏标题颜色
 * @param scrollTop 滚动距离
 * @returns 导航栏标题颜色 rgba 格式
 */
export const getNavTitleColor = (scrollTop: number): string => {
  const opacity = Math.min(scrollTop / NAV_THRESHOLD, 1);
  return scrollTop > NAV_THRESHOLD ? `rgba(0, 0, 0, ${opacity})` : 'rgba(255, 255, 255, 1)';
};

/**
 * 手机号码脱敏处理
 * @param phone 手机号码
 * @returns 脱敏后的手机号码，格式：138****1234
 */
export const hidePhone = (phone: string): string => {
  if (!phone || typeof phone !== 'string') return '';

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(phone)) return phone;

  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

/**
 * 身份证号脱敏处理
 * @param idCard 身份证号
 * @returns 脱敏后的身份证号，格式：1234**********5678
 */
export const hideIdCard = (idCard: string): string => {
  if (!idCard || typeof idCard !== 'string') return '';

  // 验证身份证号格式（15位或18位）
  const idCardRegex = /^(\d{6})\d{8,12}(\d{4})$/;
  if (!idCardRegex.test(idCard)) return idCard;

  return idCard.replace(/(\d{6})\d{8,12}(\d{4})/, '$1**********$2');
};

/**
 * 获取导航栏高度
 * @returns 导航栏高度（单位：px）
 */
export const getNavBarHeight = (): number => {
  try {
    const menuButton = uni.getMenuButtonBoundingClientRect();
    const systemInfo = uni.getSystemInfoSync();

    if (!menuButton.top || !menuButton.height) {
      return 44; // 默认导航栏高度
    }

    const statusBarHeight = systemInfo.statusBarHeight || 0;
    return menuButton.height + (menuButton.top - statusBarHeight) * 2;
  } catch (error) {
    console.warn('获取导航栏高度失败:', error);
    return 44; // 默认导航栏高度
  }
};

/**
 * 认证状态枚举
 */
export enum AuthStatus {
  /** 未认证 */
  NOT_AUTHENTICATED = 0,
  /** 已认证 */
  AUTHENTICATED = 1,
  /** 审核中 */
  UNDER_REVIEW = 2,
}

/**
 * 认证状态CLASS属性
 * 未认证：text-orange-500/80
 * 已认证：text-green-500/80
 * 审核中：text-gray-500/80
 */
export const getAuthStatusClass = (status: number): string => {
  return status === AuthStatus.NOT_AUTHENTICATED ? 'text-orange-500/80' : status === AuthStatus.AUTHENTICATED ? 'text-theme-blue' : 'text-gray-500/80';
};

/**
 * 获取认证状态文本
 * @param status 认证状态
 * @returns 状态文本描述
 */
export const getAuthStatusText = (status: number): string => {
  const statusMap: Record<number, string> = {
    [AuthStatus.NOT_AUTHENTICATED]: '未认证',
    [AuthStatus.AUTHENTICATED]: '已认证',
    [AuthStatus.UNDER_REVIEW]: '审核中',
  };

  return statusMap[status] || '未知状态';
};

/**
 * 预览图片
 * @param url 图片URL
 * @returns 预览图片
 */
export const previewImage = (urls: string[] | string, current: number = 0): void => {
  uni.previewImage({
    current,
    urls: Array.isArray(urls) ? urls : [urls],
  });
};

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

/**
 * 生成随机32位字符串
 * @returns 随机32位字符串
 */
export const generateRandomString = (length: number = 32): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export const debounce = <T extends (...args: any[]) => any>(func: T, wait: number): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * 节流函数
 * @param func 要节流的函数
 * * @param limit 限制时间（毫秒）
 * @returns 节流后的函数
 */
export const throttle = <T extends (...args: any[]) => any>(func: T, limit: number): ((...args: Parameters<T>) => void) => {
  let inThrottle = false;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};
