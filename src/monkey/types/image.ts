import type { UserProfile, UploadResult } from './common';

/**
 * 社区信息
 */
export interface ImagePost {
  /** 帖子ID */
  id: string;
  /** 用户信息 */
  fbr: UserProfile;
  /** 发布人编号 */
  fbrbh: object;
  /** 内容文字 */
  fbnr: string;
  /** 图片列表 */
  fbtp: UploadResult[];
  /** 点赞数 */
  dzs?: number;
  /** 评论数 */
  pls?: number;
  /** 是否已点赞 */
  isLiked: boolean;
  /** 发布时间 */
  createTime?: string;
  /** 修改时间 */
  modifiedTime?: string;
  /** 审核状态 */
  shzt?: string;
  /** 审核状态key */
  shzt_key?: string;
}

/**
 * 评论信息
 */
export interface Comment {
  /** 评论ID */
  id: string;
  /** 用户信息 */
  user: UserProfile;
  /** 评论内容 */
  content: string;
  /** 点赞数 */
  likeCount: number;
  /** 是否已点赞 */
  isLiked: boolean;
  /** 评论时间 */
  createTime: string;
  /** 回复的评论ID */
  replyToId?: string;
  /** 回复的用户昵称 */
  replyToUser?: string;
}

/**
 * 点赞操作结果
 */
export interface LikeResult {
  /** 是否成功 */
  success: boolean;
  /** 当前点赞状态 */
  isLiked: boolean;
  /** 最新点赞数 */
  likeCount: number;
} 