import request from '../request';
import { AuthineTypes } from '../types';

/**
 * 获取低代码列表 不需要鉴权
 * @param params 查询参数
 * @returns 低代码列表
 */
export const getAuthineList = (params: AuthineTypes.AuthineListParams) => {
  return request({
    url: '/api/api/runtime/query/list',
    method: 'POST',
    data: params,
    source: 'authine',
  });
};

/**
 * 获取低代码列表 需要鉴权
 * @param params 查询参数
 * @returns 低代码列表
 */
export const getAuthineListIsAuth = (params: AuthineTypes.AuthineListParams) => {
  return request({
    url: '/api/api/runtime/query/list',
    method: 'POST',
    data: params,
    source: 'all',
  });
};

/**
 * 获取低代码表单
 * @param params 查询参数
 * @returns 低代码表单
 */
export const getAuthineForm = (params: AuthineTypes.AuthineFormParams) => {
  return request({
    url: '/api/api/runtime/form/load',
    method: 'GET',
    data: params,
    source: 'authine',
  });
};

/**
 * 删除低代码数据
 * @param params 查询参数
 * @returns 低代码数据
 */
export const deleteAuthineData = (params: AuthineTypes.AuthineFormDeleteParams) => {
  return request({
    url: '/api/api/runtime/query/delete_data',
    method: 'POST',
    data: params,
    source: 'authine',
  });
};

/**
 * 提交低代码表单
 * @param params 查询参数
 * @returns 低代码表单
 */
export const submitAuthineForm = (params: AuthineTypes.AuthineFormSubmitParams) => {
  return request({
    url: '/api/api/runtime/form/submitForXcx',
    method: 'POST',
    data: params,
    source: 'all',
  });
};

/**
 * 获取低代码表单回放token
 * @returns 低代码表单回放token
 */
export const getAuthineFormReplayToken = () => {
  return request({
    url: '/api/api/runtime/form/getReplayToken',
    method: 'GET',
    source: 'authine',
  });
};

/**
 * 获取字典启用记录
 * @param dicId 字典id
 * @returns 字典启用记录
 */
export const getEnableRecordsByDictionaryId = (dicId: string) => {
  return request({
    url: `/api/api/data_dictionary/getEnableRecordsByDictionaryId?dicId=${dicId}`,
    method: 'GET',
    source: 'authine',
  });
};
